# ========================================
# USER MANAGEMENT QUERIES
# ========================================
SQL_VMS_USER_DETAILS = "SELECT *  FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link  where usr.Username = 'USER_NAME'"
SQL_GET_TOTAL_NUMBER_OF_VMS_USERS = "SELECT count(*) as total_users   FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link where Enabled = 1"
SQL_VMS_VENDOR_EMAIL_DETAILS = "SELECT *  FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link  where usr.Username = 'USER_NAME'"

# ========================================
# EMAIL MANAGEMENT QUERIES
# ========================================
SQL_VMS_EMAIL_DETAILS = "SELECT * FROM [VMS_UAT].[core].[email] WHERE Link = 'LINK_ID'"
SQL_GET_RANDOM_VMS_EMAIL = "SELECT TOP 1 [Link], [Vendor], [Email] FROM [VMS_UAT].[core].[email] ORDER BY NEWID()"

# ========================================
# ATM GASPER DETAILS QUERIES
# ========================================
SQL_GET_VMS_GASPER_DETAILS = "SELECT COUNT(*) AS ROW_NUMBERS FROM [VMS_UAT].[core].[gasper_details]"
SQL_GET_VMS_GASPER_DETAILS_USING_ATM_ID = "SELECT [ID], [SERIAL_NUM], [ADDRESS], [ADDRESS2], [CITY], [REGION], [CLASS], [DATA_LINE], [BRANCH], [INSTITUTION], [OBJECT_TYPE], [ZONE], [IN_SERVICE], [OUT_OF_SERVICE], [ZIP], [EDITOR_NOTE], [IP_ADDRESS], [VENDOR_SITE_ID], [PM] FROM [VMS_UAT].[core].[gasper_details] WHERE [ID] LIKE '%atm_id%'"

# ========================================
# ATM DETAILS VALIDATION QUERIES
# ========================================
SQL_GET_ATM_DETAILS_FOR_COMPARISON = "SELECT [ID], [SERIAL_NUM], [ADDRESS], [ADDRESS2], [CITY], [REGION], [CLASS], [DATA_LINE], [BRANCH], [INSTITUTION], [OBJECT_TYPE], [ZONE], [IN_SERVICE], [OUT_OF_SERVICE], [ZIP], [EDITOR_NOTE], [IP_ADDRESS], [VENDOR_SITE_ID], [PM] FROM [VMS_UAT].[core].[gasper_details] WHERE [ID] = 'ATM_ID'"
SQL_CHECK_ATM_EXISTS = "SELECT COUNT(*) as atm_count FROM [VMS_UAT].[core].[gasper_details] WHERE [ID] = 'ATM_ID'"

# ========================================
# DASHBOARD - MAIN CALLS QUERIES (THIS WEEK)
# ========================================
SQL_GAUTENG_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'GT' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Gauteng%') SELECT * FROM ProvinceData;"
SQL_LIMPOPO_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'NP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Limpopo%') SELECT * FROM ProvinceData;"
SQL_MPUMALANGA_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'MP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Mpumalanga%') SELECT * FROM ProvinceData;"
SQL_NORTH_WEST_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'NW' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%North West%') SELECT * FROM ProvinceData;"
SQL_FREE_STATE_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'FS' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Free State%') SELECT * FROM ProvinceData;"
SQL_KWA_ZULU_NATAL_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'NL' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%zulu%') SELECT * FROM ProvinceData;"
SQL_WESTERN_CAPE_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'WC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Western Cape%') SELECT * FROM ProvinceData;"
SQL_EASTERN_CAPE_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'EC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Eastern Cape%') SELECT * FROM ProvinceData;"
SQL_NORTHERN_CAPE_MAIN_CALLS_THIS_WEEK = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY g.REGION), ProvinceData AS (SELECT 'NC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Northern Cape%') SELECT * FROM ProvinceData;"

# ========================================
# DASHBOARD - MAIN CALLS QUERIES (THIS MONTH)
# ========================================
SQL_GAUTENG_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'GT' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Gauteng%') SELECT * FROM ProvinceData;"
SQL_LIMPOPO_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'NP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Limpopo%') SELECT * FROM ProvinceData;"
SQL_MPUMALANGA_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'MP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Mpumalanga%') SELECT * FROM ProvinceData;"
SQL_NORTH_WEST_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'NW' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%North West%') SELECT * FROM ProvinceData;"
SQL_FREE_STATE_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'FS' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Free State%') SELECT * FROM ProvinceData;"
SQL_KWA_ZULU_NATAL_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'NL' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%zulu%') SELECT * FROM ProvinceData;"
SQL_WESTERN_CAPE_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'WC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Western Cape%') SELECT * FROM ProvinceData;"
SQL_EASTERN_CAPE_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'EC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Eastern Cape%') SELECT * FROM ProvinceData;"
SQL_NORTHERN_CAPE_MAIN_CALLS_THIS_MONTH = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY g.REGION), ProvinceData AS (SELECT 'NC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Northern Cape%') SELECT * FROM ProvinceData;"

# ========================================
# DASHBOARD - MAIN CALLS QUERIES (THIS YEAR)
# ========================================
SQL_GAUTENG_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'GT' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Gauteng%') SELECT * FROM ProvinceData;"
SQL_LIMPOPO_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'NP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Limpopo%') SELECT * FROM ProvinceData;"
SQL_MPUMALANGA_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'MP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Mpumalanga%') SELECT * FROM ProvinceData;"
SQL_NORTH_WEST_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'NW' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%North West%') SELECT * FROM ProvinceData;"
SQL_FREE_STATE_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'FS' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Free State%') SELECT * FROM ProvinceData;"
SQL_KWA_ZULU_NATAL_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'NL' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%zulu%') SELECT * FROM ProvinceData;"
SQL_WESTERN_CAPE_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'WC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Western Cape%') SELECT * FROM ProvinceData;"
SQL_EASTERN_CAPE_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'EC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Eastern Cape%') SELECT * FROM ProvinceData;"
SQL_NORTHERN_CAPE_MAIN_CALLS_THIS_YEAR = "WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY g.REGION), ProvinceData AS (SELECT 'NC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Northern Cape%') SELECT * FROM ProvinceData;"

# ========================================
# DASHBOARD - SLA STATUS PER MAIN VENDOR QUERIES
# ========================================
SQL_SLA_STATUS_PER_MAIN_VENDOR_THIS_WEEK = "WITH VendorMTTRStatus AS (SELECT v.[description] AS Vendor, core.fnGetMTTRStatus(t.[Start Date], t.[End Date], z.decZone) AS MTTRStatus FROM core.ticket_history t INNER JOIN core.newzones z ON z.[ATM Number] = t.[ATM Number] INNER JOIN main.vendor v ON v.link = t.[Vendor_link] WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(day,t.[start date],GETDATE())<7 OR DATEDIFF(day,t.[warning date],GETDATE())<7) AND t.[End Date] IS NULL GROUP BY v.[description], t.[Start Date], t.[End Date], z.decZone) SELECT (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus LIKE '%In SLA%') AS InSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Pending SLA') AS PendingSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Approaching SLA') AS ApproachingSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Over SLA') AS OverSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'In SLA') AS InSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Pending SLA') AS PendingSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Approaching SLA') AS ApproachingSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Over SLA') AS OverSLA_BMS;"
SQL_SLA_STATUS_PER_MAIN_VENDOR_THIS_MONTH = "WITH VendorMTTRStatus AS (SELECT v.[description] AS Vendor, core.fnGetMTTRStatus(t.[Start Date], t.[End Date], z.decZone) AS MTTRStatus FROM core.ticket_history t INNER JOIN core.newzones z ON z.[ATM Number] = t.[ATM Number] INNER JOIN main.vendor v ON v.link = t.[Vendor_link] WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(day,t.[start date],GETDATE())<30 OR DATEDIFF(day,t.[warning date],GETDATE())<30) AND t.[End Date] IS NULL GROUP BY v.[description], t.[Start Date], t.[End Date], z.decZone) SELECT (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus LIKE '%In SLA%') AS InSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Pending SLA') AS PendingSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Approaching SLA') AS ApproachingSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Over SLA') AS OverSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'In SLA') AS InSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Pending SLA') AS PendingSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Approaching SLA') AS ApproachingSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Over SLA') AS OverSLA_BMS;"
SQL_SLA_STATUS_PER_MAIN_VENDOR_THIS_YEAR = "WITH VendorMTTRStatus AS (SELECT v.[description] AS Vendor, core.fnGetMTTRStatus(t.[Start Date], t.[End Date], z.decZone) AS MTTRStatus FROM core.ticket_history t INNER JOIN core.newzones z ON z.[ATM Number] = t.[ATM Number] INNER JOIN main.vendor v ON v.link = t.[Vendor_link] WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(day,t.[start date],GETDATE())<365 OR DATEDIFF(day,t.[warning date],GETDATE())<365) AND t.[End Date] IS NULL GROUP BY v.[description], t.[Start Date], t.[End Date], z.decZone) SELECT (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus LIKE '%In SLA%') AS InSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Pending SLA') AS PendingSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Approaching SLA') AS ApproachingSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BYTES%' AND MTTRStatus = 'Over SLA') AS OverSLA_BYTES, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'In SLA') AS InSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Pending SLA') AS PendingSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Approaching SLA') AS ApproachingSLA_BMS, (SELECT COUNT(Vendor) FROM VendorMTTRStatus WHERE Vendor LIKE '%BMS%' AND MTTRStatus = 'Over SLA') AS OverSLA_BMS;"

# ========================================
# DASHBOARD - TOP 10 ATM QUERIES (PARAMETERIZED)
# ========================================
# Use RANK_NUMBER and DAYS_BACK as parameters
SQL_TOP_10_ATM_BY_RANK_THIS_WEEK = "WITH RankedATMs AS ( SELECT ROW_NUMBER() OVER (ORDER BY q.AllCalls DESC) AS [Rank], q.ATMNo, q.ATMDet, q.MainCalls, q.SMCalls, q.AllCalls FROM ( SELECT DISTINCT a.[ATM Number] AS ATMNo, a.ADDRESS + ', ' + a.CITY + ', ' + a.Region AS ATMDet, ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 1 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 7 OR DATEDIFF(DAY, [warning date], GETDATE()) < 7)  ) AS MainCalls, ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 2 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 7 OR DATEDIFF(DAY, [warning date], GETDATE()) < 7) ) AS SMCalls, ( ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 1 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 7 OR DATEDIFF(DAY, [warning date], GETDATE()) < 7) ) + ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 2 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 7 OR DATEDIFF(DAY, [warning date], GETDATE()) < 7) ) ) AS AllCalls FROM ( SELECT t.[ATM Number], g.ADDRESS, g.ADDRESS2, g.CITY, g.REGION FROM core.ticket_history t INNER JOIN core.gasper_details g ON g.ID = t.[ATM Number] WHERE (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) ) a ) AS q ) SELECT [Rank], ATMNo, ATMDet, MainCalls, SMCalls, AllCalls FROM RankedATMs WHERE [Rank] = RANK_NUMBER;"
SQL_TOP_10_ATM_BY_RANK_THIS_MONTH = "WITH RankedATMs AS ( SELECT ROW_NUMBER() OVER (ORDER BY q.AllCalls DESC) AS [Rank], q.ATMNo, q.ATMDet, q.MainCalls, q.SMCalls, q.AllCalls FROM ( SELECT DISTINCT a.[ATM Number] AS ATMNo, a.ADDRESS + ', ' + a.CITY + ', ' + a.Region AS ATMDet, ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 1 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 30 OR DATEDIFF(DAY, [warning date], GETDATE()) < 30)  ) AS MainCalls, ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 2 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 30 OR DATEDIFF(DAY, [warning date], GETDATE()) < 30) ) AS SMCalls, ( ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 1 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 30 OR DATEDIFF(DAY, [warning date], GETDATE()) < 30) ) + ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 2 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 30 OR DATEDIFF(DAY, [warning date], GETDATE()) < 30) ) ) AS AllCalls FROM ( SELECT t.[ATM Number], g.ADDRESS, g.ADDRESS2, g.CITY, g.REGION FROM core.ticket_history t INNER JOIN core.gasper_details g ON g.ID = t.[ATM Number] WHERE (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) ) a ) AS q ) SELECT [Rank], ATMNo, ATMDet, MainCalls, SMCalls, AllCalls FROM RankedATMs WHERE [Rank] = RANK_NUMBER;"
SQL_TOP_10_ATM_BY_RANK_THIS_YEAR = "WITH RankedATMs AS ( SELECT ROW_NUMBER() OVER (ORDER BY q.AllCalls DESC) AS [Rank], q.ATMNo, q.ATMDet, q.MainCalls, q.SMCalls, q.AllCalls FROM ( SELECT DISTINCT a.[ATM Number] AS ATMNo, a.ADDRESS + ', ' + a.CITY + ', ' + a.Region AS ATMDet, ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 1 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 365 OR DATEDIFF(DAY, [warning date], GETDATE()) < 365)  ) AS MainCalls, ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 2 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 365 OR DATEDIFF(DAY, [warning date], GETDATE()) < 365) ) AS SMCalls, ( ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 1 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 365 OR DATEDIFF(DAY, [warning date], GETDATE()) < 365) ) + ( SELECT COUNT([ATM Number]) FROM core.ticket_history WHERE Mode_link = 2 AND [ATM Number] = a.[ATM Number] AND (DATEDIFF(DAY, [start date], GETDATE()) < 365 OR DATEDIFF(DAY, [warning date], GETDATE()) < 365) ) ) AS AllCalls FROM ( SELECT t.[ATM Number], g.ADDRESS, g.ADDRESS2, g.CITY, g.REGION FROM core.ticket_history t INNER JOIN core.gasper_details g ON g.ID = t.[ATM Number] WHERE (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, [warning date], GETDATE()) < 365) ) a ) AS q ) SELECT [Rank], ATMNo, ATMDet, MainCalls, SMCalls, AllCalls FROM RankedATMs WHERE [Rank] = RANK_NUMBER;"

# ========================================
# DASHBOARD - CALLS LOGGED AGAINST DEVICES QUERIES (PARAMETERIZED)
# ========================================
# Use ROW_NUMBER and DAYS_BACK as parameters
SQL_CALLS_LOGGED_AGAINST_DEVICES_BY_ROW_THIS_WEEK = "WITH RankedDevices AS ( SELECT ROW_NUMBER() OVER (ORDER BY q.AllCount DESC) AS [Row], q.Device, q.AllCount FROM ( SELECT DISTINCT d.Device, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.device_types d ON t.Device_link = d.Link WHERE (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 7) GROUP BY d.Device ) AS q ) SELECT Device, AllCount FROM RankedDevices WHERE [Row] = ROW_NUMBER;"
SQL_CALLS_LOGGED_AGAINST_DEVICES_BY_ROW_THIS_MONTH = "WITH RankedDevices AS ( SELECT ROW_NUMBER() OVER (ORDER BY q.AllCount DESC) AS [Row], q.Device, q.AllCount FROM ( SELECT DISTINCT d.Device, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.device_types d ON t.Device_link = d.Link WHERE (DATEDIFF(DAY, t.[start date], GETDATE()) < 30 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 30) GROUP BY d.Device ) AS q ) SELECT Device, AllCount FROM RankedDevices WHERE [Row] = ROW_NUMBER;"
SQL_CALLS_LOGGED_AGAINST_DEVICES_BY_ROW_THIS_YEAR = "WITH RankedDevices AS ( SELECT ROW_NUMBER() OVER (ORDER BY q.AllCount DESC) AS [Row], q.Device, q.AllCount FROM ( SELECT DISTINCT d.Device, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.device_types d ON t.Device_link = d.Link WHERE (DATEDIFF(DAY, t.[start date], GETDATE()) < 365 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 365) GROUP BY d.Device ) AS q ) SELECT Device, AllCount FROM RankedDevices WHERE [Row] = ROW_NUMBER;"
